package com.thedasagroup.suminative.ui.utils

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.os.Handler
import android.util.Log

class SoundPoolPlayer {
    var context: Context? = null
    var resId: Int = 0
    var duration: Long = 0
    var isPlaying: Boolean = false
    var loaded: Boolean = false
    var loops: Int = -1
    var listener: OnCompletionListener? = null

    private var mediaPlayer: MediaPlayer? = null

    //timing related
    var handler: Handler? = null
    var startTime: Long = 0
    var endTime: Long = 0
    var timeSinceStart: Long = 0

    fun pause() {
        if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
            endTime = System.currentTimeMillis()
            timeSinceStart += endTime - startTime
            mediaPlayer?.pause()
            isPlaying = false
        }
    }

//    fun stop() {
//        if (mediaPlayer != null) {
//            timeSinceStart = 0
//            mediaPlayer?.stop()
//            mediaPlayer?.reset()
//            isPlaying = false
//        }
//    }

    fun play() {
        loadAndPlay()
    }

    fun setOnCompletionListener(listener: OnCompletionListener?) {
        this.listener = listener
        mediaPlayer?.setOnCompletionListener(listener)
    }

    private fun loadAndPlay() {
        if (mediaPlayer == null) {
            mediaPlayer = MediaPlayer.create(context, resId)
            duration = mediaPlayer?.duration?.toLong() ?: 0

            mediaPlayer?.setOnCompletionListener { mp ->
                isPlaying = false
                Log.d("debug", "ending..")
                listener?.onCompletion(mp)
            }

            loaded = true
        }

        playIt()
    }

    private fun playIt() {
        if (loaded && !isPlaying && mediaPlayer != null) {
            Log.d("debug", "start playing..")

            if (loops > 0) {
                mediaPlayer?.isLooping = false
            }

            if (timeSinceStart > 0) {
                mediaPlayer?.seekTo(timeSinceStart.toInt())
            }

            mediaPlayer?.start()
            startTime = System.currentTimeMillis()
            isPlaying = true
        }
    }

    fun release() {
        mediaPlayer?.release()
        mediaPlayer = null
    }

    companion object {
        fun create(context: Context?, resId: Int): SoundPoolPlayer {
            val player = SoundPoolPlayer()
            player.context = context
            player.resId = resId
            return player
        }
    }
}