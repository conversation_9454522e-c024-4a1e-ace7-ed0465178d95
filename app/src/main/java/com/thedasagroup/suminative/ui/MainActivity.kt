package com.thedasagroup.suminative.ui

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Picture
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomNavigation
import androidx.compose.material.BottomNavigationItem
import androidx.compose.material.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.StarBorder
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.DarkGray
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.list.listItems
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.EncryptedNdk
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.prefs.validate
import com.thedasagroup.suminative.ui.login.LoginActivity
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderScreenTopFunction
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderState
import com.thedasagroup.suminative.ui.orders.ScheduleOrderScreenTopFunction
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.throttleFirst
import com.thedasagroup.suminative.ui.stock.StockActivity
import com.thedasagroup.suminative.ui.stores.SelectStoreActivity
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import com.thedasagroup.suminative.ui.utils.formatDateToHourAndMinute
import com.thedasagroup.suminative.ui.utils.getDateFromHourAndMinute
import com.thedasagroup.suminative.ui.utils.getDayOfWeek
import com.thedasagroup.suminative.ui.utils.getMinutesBetweenTwoDates
import com.thedasagroup.suminative.ui.utils.toGMT
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import java.util.UUID


class MainActivity : AppCompatActivity(), MavericksView {

    private val TAG: String? = MainActivity::class.simpleName
    val viewModel: LoginScreenViewModel by viewModel()
    val orderScreenViewModel: OrderScreenViewModel by viewModel()
    private var AL: ArrayList<HashMap<String, Any>> = ArrayList()

    var dialog: MaterialDialog? = null
    var isShowAllOrders: Boolean = false
    var ordersResponse: OrderResponse? = null
    var mapCount: Map<Int?, Int> = mutableMapOf()

    var dialogType: String? = null
    var dialogMessage: String? = null
    var isScheduleOrder: Boolean = false
    var showDialogProp: String? = null
    var dialogTitle: String? = null
    var currentRouteId: String = "0"

    private val pushNotificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { granted ->
        if (granted) {
            Log.v(TAG, "Permission granted")
        } else {
            Log.v(TAG, "Permission denied")
        }
    }

    @SuppressLint("CheckResult")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        lifecycleScope.launch(Dispatchers.Main) {
            orderScreenViewModel.onEach(OrderState::isShowAllOrders) {
                isShowAllOrders = it
            }
            orderScreenViewModel.onEach(OrderState::ordersResponse) { response ->
                if (response is Success) {
                    ordersResponse = response()
                }
            }
            orderScreenViewModel.onEach(OrderState::mapCount) {
                mapCount = it
            }
            orderScreenViewModel.onEach(OrderState::dialogType) {
                dialogType = it
            }
            orderScreenViewModel.onEach(OrderState::dialogMessage) {
                dialogMessage = it
            }
            orderScreenViewModel.onEach(OrderState::currentRouteId) {
                currentRouteId = it
            }
            orderScreenViewModel.onEach(OrderState::isScheduleOrder) {
                isScheduleOrder = it
            }
            orderScreenViewModel.onEach(OrderState::dialogTitle) { title ->
                if (title.isNotEmpty()) {
                    if (dialogType == "update_order_with_dialog") {
                        if (dialog?.isShowing != true) {
                            showAutoDismissDialog(
                                message = dialogMessage ?: "Update Order", title = "Update Order"
                            )
                        } else {
                            dialog?.dismiss()
                            showAutoDismissDialog(
                                message = dialogMessage ?: "Update Order",
                                title = "Update Order",
                            )
                        }
                    } else {
                        if (dialog?.isShowing != true) {
                            showDialog(
                                message = dialogMessage ?: "New Order",
                                title = dialogTitle ?: "New Order",
                                type = dialogType ?: "new_order",
                                isScheduleOrder = isScheduleOrder
                            )
                        } else {
                            dialog?.dismiss()
                            showDialog(
                                message = dialogMessage ?: "New Order",
                                title = dialogTitle ?: "New Order",
                                type = dialogType ?: "new_order",
                                isScheduleOrder = isScheduleOrder
                            )
                        }
                    }
                } else {
                    dialog?.dismiss()
                }
            }
        }

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("custom-event-name"))


        if (viewModel.prefs.loginResponse?.validate() == true) {
            setContent {
                SumiNativeTheme {
                    NavigationDrawer(myTopBar = {
                        MyTopAppBar(viewModel = orderScreenViewModel)
                    }, orderScreenViewModel = orderScreenViewModel,
                        content = {
                            RegularOrdersScreen(innerPadding = PaddingValues(top = 56.dp))
                        })
                }
            }
            lifecycleScope.launch(Dispatchers.IO) {
                delay(10000)
                orderScreenViewModel.isNavSetupDone(true)
            }
        } else {
            val intent = Intent(this, LoginActivity::class.java)
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }

    }

    override fun onResume() {
        super.onResume()
        showDialogWhilePlaying()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            pushNotificationPermissionLauncher.launch(android.Manifest.permission.POST_NOTIFICATIONS)
        }
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.updateShouldRefresh(shouldRefresh = true)
//            orderScreenViewModel.getOrders(isShowAllOrders = isShowAllOrders)
//            orderScreenViewModel.getScheduleOrders()
        }
        SunmiPrintHelper.getInstance().initSunmiPrinterService(this)
        SunmiPrintHelper.getInstance().initPrinter()
        lifecycleScope.launch(Dispatchers.IO) {
            checkStoreClosed()
            delay(20000)
            orderScreenViewModel.soundPoolPlayer.pause()
        }
    }

    private suspend fun checkStoreClosed() {
        orderScreenViewModel.getStoreSettings.invoke().collectLatest { settings ->
            when (settings) {
                is Success -> {
                    withContext(Dispatchers.IO) {
                        withContext(Dispatchers.Main) {
                            viewModel.prefs.storeSettings =
                                settings().myStoreSettings?.storeSettings
                        }
                        val nowDate = orderScreenViewModel.trueTimeImpl.now().toGMT()
                        val weekday: String = nowDate.getDayOfWeek()
                        val timing =
                            settings().myStoreSettings?.storeSettings?.timingJson?.firstOrNull {
                                it.day == weekday
                            }
                        val openHour = timing?.openingTime?.split(":")?.get(0)?.toInt() ?: 0
                        val openMinute = timing?.openingTime?.split(":")?.get(1)?.toInt() ?: 0
                        val closeHour = timing?.closingTime?.split(":")?.get(0)?.toInt() ?: 0
                        val closeMinute = timing?.closingTime?.split(":")?.get(1)?.toInt() ?: 0
                        val openDate = getDateFromHourAndMinute(
                            openHour, openMinute, nowDate = nowDate
                        ).toGMT()
                        val closeDate = getDateFromHourAndMinute(
                            closeHour, closeMinute, nowDate = nowDate
                        ).toGMT()
                        orderScreenViewModel.updateStoreTimings(
                            openingTime = formatDateToHourAndMinute(openDate),
                            closeTime = formatDateToHourAndMinute(
                                closeDate
                            )
                        )
                        if (nowDate.after(openDate) && nowDate.before(closeDate)) {
                            orderScreenViewModel.updateStoreCloseSettings(storeCloseSettings = "store_open")
                            viewModel.prefs.storeClosed = false
                        } else {
                            orderScreenViewModel.updateStoreCloseSettings(storeCloseSettings = "store_close")
                            viewModel.prefs.storeClosed = true
                        }
                    }
                }

                else -> {
                }
            }
        }
    }

    val functionSilentOrders = throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
        lifecycleScope.launch(Dispatchers.IO) {
//            callOrders()
        }
    }

    val functionOpenCloseStore =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
            lifecycleScope.launch(Dispatchers.IO) {
                checkStoreClosed()
            }
        }

    val function = throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 60000) {
        lifecycleScope.launch(Dispatchers.IO) {
            updateOrderCountMinutes()
        }
    }

    val functionNewOrders =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
            lifecycleScope.launch(Dispatchers.IO) {
                orderScreenViewModel.updateShouldRefresh(shouldRefresh = true)
            }
        }

    val functionOtherOrders =
        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 100) {
            lifecycleScope.launch(Dispatchers.IO) {
                withContext(Dispatchers.Main) {
                    showDialogWhilePlaying()
                }
            }
        }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // Get extra data included in the Intent
            val message = intent.getStringExtra("message")
            val type = intent.getStringExtra("type")
            val isScheduleOrder = intent.getBooleanExtra("isScheduleOrder", false)
            if (type == "new_order" || type == "cancel_order") {
                functionNewOrders(Unit)
//                val title = if (dialogType == "new_order") "New Order" else "Order Cancelled"
//                orderScreenViewModel.updateShowDialog(
//                    dialogType = type,
//                    dialogMessage = message ?: "",
//                    showDialog = UUID.randomUUID().toString(),
//                    dialogTitle = title,
//                    isScheduleOrder = isScheduleOrder
//                )
            } else if (type == "Network") {
//                if (ordersResponse?.orders?.isNotEmpty() != true) {
//                    val function =
//                        throttleFirst<Unit>(coroutineScope = lifecycleScope, skipMs = 1000) {
//                            lifecycleScope.launch(Dispatchers.IO) {
//                                callOrders()
//                            }
//                        }
//                    function(Unit)
//                }
            } else if (type == "update_orders_loop") {
//                if (EndlessSocketService.dialogType == "new_order" || EndlessSocketService.dialogType == "cancel_order") {
//                    functionNewOrders(Unit)
//                } else {
//                    functionOtherOrders(Unit)
//                }
//                if (ordersResponse?.orders?.isNotEmpty() == true) {
//                    function(Unit)
//                }
                orderScreenViewModel.updateIsConnected(
                    isConnected2 = EndlessSocketService.flowIsConnected2.value
                )
            } else if (type == "unknown_status") {
                functionSilentOrders(Unit)
            } else if (type == "update_store_timings") {
                functionOpenCloseStore(Unit)
            } else if (type == "update_order_with_dialog") {
                val title = "Update Order"
                orderScreenViewModel.updateShowDialog(
                    dialogType = type,
                    dialogMessage = message ?: "",
                    showDialog = UUID.randomUUID().toString(),
                    dialogTitle = title
                )
            } else if (type == "store_open_close") {
                lifecycleScope.launch(Dispatchers.IO) {
                    orderScreenViewModel.getIsClosed()
                }
            } else {
                functionSilentOrders(Unit)
            }
        }
    }

    fun updateOrderCountMinutes() {
        val orders = ordersResponse?.orders?.map {
            if (it.order?.acceptedDate?.isNotEmpty() == true) {
                val dateFormat =
                    if (it.order.acceptedDate.contains("T")) DATE_FORMAT_BACK_END else DATE_FORMAT_APP
                if (getMinutesBetweenTwoDates(
                        startDateString = it.order.createdOn ?: "",
                        endDateString = orderScreenViewModel.getCurrentUTC(),
                        formatStart = dateFormat,
                        formatEnd = DATE_FORMAT_APP
                    ) > 0
                ) {
                    val order = it.order.copy(
                        countMinutes = getMinutesBetweenTwoDates(
                            startDateString = it.order.createdOn ?: "",
                            endDateString = orderScreenViewModel.getCurrentUTC(),
                            formatStart = dateFormat,
                            formatEnd = DATE_FORMAT_APP
                        ).toInt()
                    )
                    it.copy(order = order)
                } else it
            } else it
        }
        orderScreenViewModel.updateOrders(
            ordersResponse = OrderResponse(
                orders = orders, success = true
            )
        )
    }

    fun showDialogWhilePlaying() {
        if (orderScreenViewModel.audioManager.isMusicActive && orderScreenViewModel.soundPoolPlayer.isPlaying) {
            orderScreenViewModel.updateShowDialog(
                dialogMessage = EndlessSocketService.dialogMessage,
                dialogTitle = when (EndlessSocketService.dialogType) {
                    "new_order" -> "New Order"
                    "cancel_order" -> "Order Cancelled"
                    else -> "Update Order"
                },
                dialogType = EndlessSocketService.dialogType,
                showDialog = UUID.randomUUID().toString()
            )
        } else {
            if (dialog?.isShowing == true && EndlessSocketService.dialogType != "new_order" && EndlessSocketService.dialogType != "cancel_order") {
                lifecycleScope.launch(Dispatchers.IO) {
//                    callOrders()
                }
                dismissDialog()
            }
        }

    }

    fun dismissDialog() {
        EndlessSocketService.dialogType = ""
        EndlessSocketService.dialogMessage = ""
        orderScreenViewModel.updateShowDialog(
            dialogMessage = "", dialogTitle = "", dialogType = "", showDialog = ""
        )
    }

    fun showDialog(message: String, title: String, type: String, isScheduleOrder: Boolean = false) {
        if (message.isNotEmpty() && title.isNotEmpty()) {
            dialog = MaterialDialog(this@MainActivity)
            val layout =
                if (type == "new_order") R.layout.dialog_new_order else R.layout.dialog_cancel_order
            dialog?.setContentView(layout)
            val tvTitle = dialog?.findViewById<TextView>(R.id.tvTitle)
            tvTitle?.text = title
            val tvMessage = dialog?.findViewById<TextView>(R.id.tvMessage)
            tvMessage?.text = message
            val btnOk = dialog?.findViewById<TextView>(R.id.btnOk)
            btnOk?.setOnClickListener {
                dialog?.dismiss()
                dismissDialog()
                lifecycleScope.launch(Dispatchers.IO) {
                    withContext(Dispatchers.Main) {
                        orderScreenViewModel.soundPoolPlayer.pause()
                    }
//                    callOrders()
                    if (isScheduleOrder) {
                        orderScreenViewModel.updateCurrentRoute("1")
                    } else {
                        orderScreenViewModel.updateCurrentRoute("0")
                    }
                }
            }
            lifecycleScope.launch(Dispatchers.IO) {
                delay(2 * 60 * 1000)
                withContext(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.pause()
                    dismissDialog()
                    callOrdersSilent()
                }
            }
            dialog?.cancelable(false)
            dialog?.cancelOnTouchOutside(false)
//            dialog?.show()
            orderScreenViewModel.updateDialogType(dialogType = type)
        }
    }

    fun showAutoDismissDialog(message: String, title: String) {
        if (message.isNotEmpty() && title.isNotEmpty()) {
            dialog = MaterialDialog(this@MainActivity)
            val layout = R.layout.dialog_update_order
            dialog?.setContentView(layout)
            val tvTitle = dialog?.findViewById<TextView>(R.id.tvTitle)
            tvTitle?.text = title
            val tvMessage = dialog?.findViewById<TextView>(R.id.tvMessage)
            tvMessage?.text = message
            val tvOrderId = dialog?.findViewById<TextView>(R.id.tvOrderId)
            val btnOk = dialog?.findViewById<TextView>(R.id.btnOk)
            dialog?.cancelable(false)
            btnOk?.setOnClickListener {
                lifecycleScope.launch(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.pause()
                    dismissDialog()
                    dialog?.dismiss()
                    callOrdersSilent()
                }
            }
            dialog?.cancelOnTouchOutside(false)
            dialog?.show()
            orderScreenViewModel.updateDialogType(dialogType = "update_order")
            lifecycleScope.launch(Dispatchers.IO) {
                delay(10 * 1000)
                withContext(Dispatchers.Main) {
                    orderScreenViewModel.soundPoolPlayer.pause()
                    dismissDialog()
                    dialog?.dismiss()
                    callOrdersSilent()
                }
            }
        }
    }

    override fun invalidate() {

    }

    override fun onDestroy() {
        // Unregister since the activity is about to be closed.
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver)
//        LocalBroadcastManager.getInstance(this).unregisterReceiver(networkChangeLocalMessageReceiver)
        super.onDestroy()
    }

    @Composable
    fun RegularOrdersScreen(innerPadding: PaddingValues) {
        OrderScreenTopFunction(modifier = Modifier.padding(innerPadding),
            viewModel = orderScreenViewModel,
            onPrintBill = { bitmap ->
                printOrderBitmap(bitmap, this)
            },
            onTrackingUrlClick = { url ->

                val finalUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    "http://$url"
                } else url

                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(finalUrl))
                val packageManager: PackageManager = packageManager
                if (browserIntent.resolveActivity(packageManager) != null) {
                    startActivity(browserIntent)
                } else {
                    MaterialDialog(this@MainActivity).show {
                        title(text = "Error")
                        message(text = "No browser found to open the tracking url")
                        positiveButton(text = "Ok") {
                            it.dismiss()
                        }
                    }
                }

            },
            onUpdateShowAllOrders = {
//                orderScreenViewModel.updateShowAllOrders(it)
//                lifecycleScope.launch(Dispatchers.IO) {
//                    orderScreenViewModel.getOrders(isShowAllOrders = it)
//                    orderScreenViewModel.getScheduleOrders()
//                }
            },
            callOrders = {
                showDialogWhilePlaying()
            })
    }

    @Composable
    fun ScheduleOrdersScreen(innerPadding: PaddingValues) {
        ScheduleOrderScreenTopFunction(modifier = Modifier.padding(innerPadding),
            viewModel = orderScreenViewModel,
            onPrintBill = { bitmap ->
                printOrderBitmap(bitmap, this)
            },
            onTrackingUrlClick = { url ->

                val finalUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    "http://$url"
                } else url

                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(finalUrl))
                val packageManager: PackageManager = packageManager
                if (browserIntent.resolveActivity(packageManager) != null) {
                    startActivity(browserIntent)
                } else {
                    MaterialDialog(this@MainActivity).show {
                        title(text = "Error")
                        message(text = "No browser found to open the tracking url")
                        positiveButton(text = "Ok") {
                            it.dismiss()
                        }
                    }
                }

            },
            onUpdateShowAllOrders = {
//                orderScreenViewModel.updateShowAllOrders(it)
//                lifecycleScope.launch(Dispatchers.IO) {
//                    orderScreenViewModel.getOrders(isShowAllOrders = it)
//                    orderScreenViewModel.getScheduleOrders()
//                }
            },
            callOrders = {
                showDialogWhilePlaying()
            })
    }

    @SuppressLint("CheckResult")
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun MyTopAppBar(viewModel: OrderScreenViewModel) {
        val isConnected2 by viewModel.collectAsState(OrderState::isConnected2)
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            val store = viewModel.prefs.store
            val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${store?.banner}"
            val request: ImageRequest =
                ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                    .crossfade(true).diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                    .setHeader("Cache-Control", "max-age=31536000").build()
            Spacer(modifier = Modifier.padding(4.dp))
            AsyncImage(
                modifier = Modifier.size(40.dp), model = request, contentDescription = ""
            )
            Spacer(modifier = Modifier.padding(4.dp))
            Text(
                text = store?.name ?: "",
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .width(170.sdp)
                    .wrapContentHeight() // make text center vertical
            )
            Spacer(modifier = Modifier.padding(4.dp))
            Box(Modifier.padding(10.dp)) {
                Image(
                    imageVector = Icons.Default.AccountCircle,
                    colorFilter = ColorFilter.tint(color = Color.Black),
                    contentDescription = "Profile",
                    modifier = Modifier
                        .size(20.sdp)
                        .clickable {
                            val myItems = listOf(
                                "Change Store", "Logout", "Stock Management", "Cancel"
                            )
                            dialog = MaterialDialog(this@MainActivity)
                            dialog?.cancelable(false)
                            dialog?.cancelOnTouchOutside(false)
                            dialog?.show {
                                title(text = "Profile")
                                listItems(items = myItems) { dialog, index, text ->
                                    when (text) {
                                        "Logout" -> {
                                            //are you sure you want to logout?
                                            MaterialDialog(this@MainActivity).show {
                                                title(text = "Logout")
                                                message(text = "Are you sure you want to logout?")
                                                positiveButton(text = "Yes") {
                                                    viewModel.prefs.loginResponse = null
                                                    viewModel.prefs.store = null
                                                    dismissDialog()
                                                    val intent = Intent(
                                                        this@MainActivity,
                                                        LoginActivity::class.java
                                                    )
                                                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                                                    startActivity(intent)
                                                }
                                                negativeButton(text = "No") {
                                                    dismissDialog()
                                                }
                                            }
                                        }

                                        "Change Store" -> {
                                            viewModel.prefs.store = null
                                            dismissDialog()
                                            val intent = Intent(
                                                this@MainActivity,
                                                SelectStoreActivity::class.java
                                            )
                                            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                                            startActivity(intent)
                                        }

                                        "Stock Management" -> {
                                            dismissDialog()
                                            val intent = Intent(
                                                this@MainActivity,
                                                StockActivity::class.java
                                            )
                                            startActivity(intent)
                                        }

                                        "Cancel" -> {
                                            dismissDialog()
                                        }
                                    }
                                }
                            }
                        }
                        .clip(
                            RoundedCornerShape(30.sdp)
                        ),
                    contentScale = ContentScale.Crop,
                )
                Box(
                    modifier = Modifier
                        .padding(end = 3.sdp, bottom = 1.sdp)
                        .size(7.sdp)
                        .clip(CircleShape)
                        .align(Alignment.BottomEnd)
                        .background(color = if (isConnected2) Color.Green else Color.Red)
                )
            }
            Spacer(modifier = Modifier.padding(4.dp))
        }
    }

    @Composable
    fun MyBottomAppBar(navController: NavController) {
        val currentRouteId by orderScreenViewModel.collectAsState(OrderState::currentRouteId)
        val isNavSetupDone by orderScreenViewModel.collectAsState(OrderState::isNavSetupDone)
        if (isNavSetupDone) {
            if (currentRouteId == "0") {
                navigate(navController, topLevelRoutes[0])
            } else {
                navigate(navController, topLevelRoutes[1])
            }
        }
        BottomNavigation(
            elevation = 0.dp, contentColor = Color.White, backgroundColor = DarkGray.copy(0.6f)
        ) {
            val navBackStackEntry by navController.currentBackStackEntryAsState()
            val currentDestination = navBackStackEntry?.destination
            topLevelRoutes.forEachIndexed { index, topLevelRoute ->
                val isSelected = index.toString() == currentRouteId
                BottomNavigationItem(
                    modifier = if (isSelected) {
                        Modifier
                            .clip(RoundedCornerShape(25.dp))
                            .background(color = DarkGray)
                            .clipToBounds()
                    } else {
                        Modifier.background(color = Color.Transparent)
                    },
                    icon = { Icon(topLevelRoute.icon, contentDescription = topLevelRoute.name) },
                    label = {
                        Text(
                            topLevelRoute.name, color = if (isSelected) Color.White else Color.Black
                        )
                    },
                    selected = isSelected,
                    onClick = {
                        orderScreenViewModel.updateCurrentRoute(index.toString())
                        navigate(navController, topLevelRoute)
                    },
                    selectedContentColor = Color.White,
                    unselectedContentColor = Color.White,
                    alwaysShowLabel = true,
                )
            }
        }
    }

    fun navigate(navController: NavController, topLevelRoute: TopLevelRoute<out Any>) {
        navController.navigate(topLevelRoute.route) {
            // Pop up to the start destination of the graph to
            // avoid building up a large stack of destinations
            // on the back stack as users select items
            popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
            }
            // Avoid multiple copies of the same destination when
            // reselecting the same item
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }
    }

//    fun callOrders() {
//        lifecycleScope.launch(Dispatchers.IO) {
//            orderScreenViewModel.getOrders(isShowAllOrders = isShowAllOrders)
//        }
//        lifecycleScope.launch(Dispatchers.IO) {
//            orderScreenViewModel.getScheduleOrders()
//        }
//    }

    fun callOrdersSilent() {
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getOrdersSilent(isShowAllOrders = isShowAllOrders)
        }
        lifecycleScope.launch(Dispatchers.IO) {
            orderScreenViewModel.getScheduleOrdersSilent()
        }
    }
}

fun createBitmapFromPicture(picture: Picture): Bitmap {
    val bitmap = Bitmap.createBitmap(
        picture.width, picture.height, Bitmap.Config.ARGB_8888
    )

    val canvas = android.graphics.Canvas(bitmap)
    canvas.drawColor(android.graphics.Color.WHITE)
    canvas.drawPicture(picture)
    return bitmap
}

fun printOrderBitmap(bitmap: Bitmap, context: Context) {
    SunmiPrintHelper.getInstance().initSunmiPrinterService(context)
    SunmiPrintHelper.getInstance().initPrinter()
    SunmiPrintHelper.getInstance().feedPaper()
    SunmiPrintHelper.getInstance().printBitmap(bitmap, 0)
    SunmiPrintHelper.getInstance().feedPaper()
}

data class TopLevelRoute<T : Any>(val name: String, val route: T, val icon: ImageVector)

val topLevelRoutes = listOf(
    TopLevelRoute("Regular", Regular("Regular"), Icons.Default.Home),
    TopLevelRoute("Scheduled", ScheduleOrders("ScheduleOrders"), Icons.Default.StarBorder)
)

@Serializable
data class Regular(val id: String)

@Serializable
data class ScheduleOrders(val id: String)