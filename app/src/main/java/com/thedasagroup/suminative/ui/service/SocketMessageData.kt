package com.thedasagroup.suminative.ui.service

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SocketMessageData(
    @SerialName("message")
    val message: String? = null,
    @SerialName("orderId")
    val orderId: Int? = 0,
    @SerialName("sendToKDS")
    val sendToKDS: Boolean? = false,
    @SerialName("storeId")
    val storeId: Int? = 0
)