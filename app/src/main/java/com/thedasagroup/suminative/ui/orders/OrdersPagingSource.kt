package com.thedasagroup.suminative.ui.orders

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.first

class OrdersPagingSource(
    private val repository: OrdersRepository,
    private val prefs: Prefs,
    private val isShowAllOrders: Boolean
) : PagingSource<Int, OrderItem>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, OrderItem> {
        return try {
            val pageNumber = params.key ?: 0
            val pageSize = params.loadSize

            val request = GetPagedOrderRequest(
                storeId = prefs.store?.id ?: 0,
                pageNumber = pageNumber,
                pageSize = pageSize,
                userId = prefs.loginResponse?.user?.id ?: -1
            )

            val response = repository.getAcceptedOrders(request = request)

            when (response.value) {
                is Success -> {
                    val orderResponse = response.value()
                    val orders = orderResponse?.orders ?: emptyList()
                    val totalCount = 1000

                    LoadResult.Page(
                        data = orders,
                        prevKey = if (pageNumber == 0) null else pageNumber - 1,
                        nextKey = if (orders.isEmpty() || (pageNumber + 1) * pageSize >= totalCount) null else pageNumber + 1
                    )
                }
                else -> {
                    LoadResult.Error(Exception("Failed to load orders"))
                }
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, OrderItem>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}
