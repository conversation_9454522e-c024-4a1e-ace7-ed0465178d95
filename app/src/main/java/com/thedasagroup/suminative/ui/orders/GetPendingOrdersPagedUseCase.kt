package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.StateFlow

open class GetPendingOrdersPagedUseCase(
    private val repo: OrdersRepository,
    private val prefs: Prefs
) {
    suspend operator fun invoke(isShowAllOrder: Boolean): StateFlow<Async<OrderResponse>> {

        val orderResponse = if(isShowAllOrder){
            repo.getPagedAllOrders(
                request = GetPagedOrderRequest(
                    storeId = prefs.store?.id ?: 0,
                    pageNumber = 0,
                    pageSize = 10,
                    userId = prefs.loginResponse?.user?.id ?: -1
                )
            )
        }
        else{
            repo.getPagedPendingOrders(
                request = GetPagedOrderRequest(
                    storeId = prefs.store?.id ?: 0,
                    pageNumber = 0,
                    pageSize = 10,
                    userId = prefs.loginResponse?.user?.id ?: -1
                )
            )
        }

        return orderResponse
    }
}