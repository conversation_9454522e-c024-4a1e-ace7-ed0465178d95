package com.thedasagroup.suminative.data.api

import android.util.Log
import com.pluto.plugins.network.ktor.PlutoKtorInterceptor
import io.ktor.client.HttpClient
import io.ktor.client.engine.android.Android
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.observer.ResponseObserver
import io.ktor.client.request.accept
import io.ktor.client.request.header
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import com.thedasagroup.suminative.BuildConfig

val apiClient = HttpClient(Android) {

    install(ContentNegotiation) {
        json(
            Json {
                prettyPrint = true
                isLenient = true
                useAlternativeNames = true
                ignoreUnknownKeys = true
                encodeDefaults = false
                explicitNulls = false
            }
        )
    }

    install(PlutoKtorInterceptor)

    install(HttpTimeout) {
        requestTimeoutMillis = 3 * 60000
        connectTimeoutMillis = 3 * 60000
        socketTimeoutMillis = 3 * 60000
    }

    install(Logging) {
        logger = object : Logger {
            override fun log(message: String) {
                Log.v("Logger Ktor =>", message)
            }
        }
        level = LogLevel.ALL
    }

    install(ResponseObserver) {
        onResponse { response ->
            Log.d("HTTP status:", "${response.status.value}")
        }
    }

    install(DefaultRequest) {
        header(HttpHeaders.ContentType, ContentType.Application.Json)
    }

    defaultRequest {
        contentType(ContentType.Application.Json)
        accept(ContentType.Application.Json)
    }
}

val DOMAIN_ONLY = BuildConfig.DOMAIN_NAME
val BASE_DOMAIN ="https://${DOMAIN_ONLY}"
val BASE_URL = "${BASE_DOMAIN}/dasa/v1/api/requestHandler"
val SOCKET = "wss://${DOMAIN_ONLY}/BackendDASA-1.0.0/ws"
val GET_PENDING_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getPendingOrders"
val GET_POS_SETTINGS = "${BASE_DOMAIN}/BackendDASA-1.0.0/getPOSSettings"
val GET_ALL_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getAllOrders"
val GET_ACCEPTED_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getAllPOSOrdersKDS"
val GET_SCHEDULED_ORDERS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getScheduledItems"
val GET_STOCK_ITEMS = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/getAllItems"
val EDIT_STOCK = "${BASE_DOMAIN}/BackendDASA-1.0.0/store/editStock"
val LOGIN = "${BASE_DOMAIN}/BackendDASA-1.0.0/login/store"
