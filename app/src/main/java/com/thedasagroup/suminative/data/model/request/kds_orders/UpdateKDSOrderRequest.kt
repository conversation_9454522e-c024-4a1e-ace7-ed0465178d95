package com.thedasagroup.suminative.data.model.request.kds_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UpdateKDSOrderRequest(
    @SerialName("customerId")
    val customerId: Int? = null,
    @SerialName("tableId")
    val tableId: Int? = null,
    @SerialName("tableName")
    val tableName: String? = null,
    @SerialName("cartJson")
    val cartJson: String? = null
)
