package com.thedasagroup.suminative.data.model.response.kds_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class KDSOrdersResponse(
    @SerialName("content")
    val content: List<KDSOrder>? = null,
    @SerialName("pageable")
    val pageable: Pageable? = null,
    @SerialName("last")
    val last: Boolean? = null,
    @SerialName("totalPages")
    val totalPages: Int? = null,
    @SerialName("totalElements")
    val totalElements: Int? = null,
    @SerialName("size")
    val size: Int? = null,
    @SerialName("number")
    val number: Int? = null,
    @SerialName("sort")
    val sort: Sort? = null,
    @SerialName("first")
    val first: Boolean? = null,
    @SerialName("numberOfElements")
    val numberOfElements: Int? = null,
    @SerialName("empty")
    val empty: Boolean? = null,
    val success: Boolean = false
)

@Serializable
data class Pageable(
    @SerialName("sort")
    val sort: Sort? = null,
    @SerialName("pageNumber")
    val pageNumber: Int? = null,
    @SerialName("pageSize")
    val pageSize: Int? = null,
    @SerialName("offset")
    val offset: Int? = null,
    @SerialName("unpaged")
    val unpaged: Boolean? = null,
    @SerialName("paged")
    val paged: Boolean? = null
)

@Serializable
data class Sort(
    @SerialName("sorted")
    val sorted: Boolean? = null,
    @SerialName("unsorted")
    val unsorted: Boolean? = null,
    @SerialName("empty")
    val empty: Boolean? = null
)
