package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.GET_ACCEPTED_ORDERS
import com.thedasagroup.suminative.data.api.GET_ALL_ORDERS
import com.thedasagroup.suminative.data.api.GET_PENDING_ORDERS
import com.thedasagroup.suminative.data.api.GET_SCHEDULED_ORDERS
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.notification.NotificationRequest
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.notification.NotificationResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class KDSOrdersRepository : BaseRepository() {

}