package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.CHANGE_KDS_ORDER_STATUS
import com.thedasagroup.suminative.data.api.CREATE_KDS_ORDER
import com.thedasagroup.suminative.data.api.DELETE_KDS_ORDER
import com.thedasagroup.suminative.data.api.GET_ACCEPTED_ORDERS
import com.thedasagroup.suminative.data.api.GET_ALL_ORDERS
import com.thedasagroup.suminative.data.api.GET_KDS_ORDERS_BY_STATUS
import com.thedasagroup.suminative.data.api.GET_KDS_ORDERS_LIST
import com.thedasagroup.suminative.data.api.GET_KDS_ORDER_BY_ID
import com.thedasagroup.suminative.data.api.GET_KDS_ORDER_BY_TABLE_ID
import com.thedasagroup.suminative.data.api.GET_PENDING_ORDERS
import com.thedasagroup.suminative.data.api.GET_SCHEDULED_ORDERS
import com.thedasagroup.suminative.data.api.UPDATE_KDS_ORDER
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.kds_orders.ChangeKDSOrderStatusRequest
import com.thedasagroup.suminative.data.model.request.kds_orders.CreateKDSOrderRequest
import com.thedasagroup.suminative.data.model.request.kds_orders.UpdateKDSOrderRequest
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.notification.NotificationRequest
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.kds_orders.KDSOrder
import com.thedasagroup.suminative.data.model.response.kds_orders.KDSOrdersResponse
import com.thedasagroup.suminative.data.model.response.notification.NotificationResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import io.ktor.client.call.body
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class KDSOrdersRepository : BaseRepository() {

    suspend fun createKDSOrder(request: CreateKDSOrderRequest): StateFlow<Async<KDSOrder>> {
        val flow = MutableStateFlow<Async<KDSOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrderResponse = apiClient.post(urlString = CREATE_KDS_ORDER) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<KDSOrder>()
                return@safeApiCall Success(kdsOrderResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun updateKDSOrder(orderId: Int, request: UpdateKDSOrderRequest): StateFlow<Async<KDSOrder>> {
        val flow = MutableStateFlow<Async<KDSOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrderResponse = apiClient.put(urlString = "$UPDATE_KDS_ORDER/$orderId") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<KDSOrder>()
                return@safeApiCall Success(kdsOrderResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun changeKDSOrderStatus(request: ChangeKDSOrderStatusRequest): StateFlow<Async<KDSOrder>> {
        val flow = MutableStateFlow<Async<KDSOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrderResponse = apiClient.patch(urlString = CHANGE_KDS_ORDER_STATUS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<KDSOrder>()
                return@safeApiCall Success(kdsOrderResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getKDSOrdersList(
        storeId: Int,
        status: Int? = null,
        page: Int = 0,
        size: Int = 20
    ): StateFlow<Async<KDSOrdersResponse>> {
        val flow = MutableStateFlow<Async<KDSOrdersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrdersResponse = apiClient.get(urlString = GET_KDS_ORDERS_LIST) {
                    parameter("storeId", storeId)
                    status?.let { parameter("status", it) }
                    parameter("page", page)
                    parameter("size", size)
                }.body<KDSOrdersResponse>()
                return@safeApiCall Success(kdsOrdersResponse.copy(success = true))
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getKDSOrderById(orderId: Int): StateFlow<Async<KDSOrder>> {
        val flow = MutableStateFlow<Async<KDSOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrderResponse = apiClient.get(urlString = "$GET_KDS_ORDER_BY_ID/$orderId")
                    .body<KDSOrder>()
                return@safeApiCall Success(kdsOrderResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getKDSOrderByTableId(tableId: Int): StateFlow<Async<KDSOrder>> {
        val flow = MutableStateFlow<Async<KDSOrder>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrderResponse = apiClient.get(urlString = "$GET_KDS_ORDER_BY_TABLE_ID/$tableId")
                    .body<KDSOrder>()
                return@safeApiCall Success(kdsOrderResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun deleteKDSOrder(orderId: Int): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val deleteResponse = apiClient.delete(urlString = "$DELETE_KDS_ORDER/$orderId")
                return@safeApiCall Success(deleteResponse.status.value == 200)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getKDSOrdersByStatus(
        storeId: Int,
        status: Int,
        page: Int = 0,
        size: Int = 50
    ): StateFlow<Async<KDSOrdersResponse>> {
        val flow = MutableStateFlow<Async<KDSOrdersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val kdsOrdersResponse = apiClient.get(urlString = GET_KDS_ORDERS_BY_STATUS) {
                    parameter("storeId", storeId)
                    parameter("status", status)
                    parameter("page", page)
                    parameter("size", size)
                }.body<KDSOrdersResponse>()
                return@safeApiCall Success(kdsOrdersResponse.copy(success = true))
            }
            flow.value = response
        }
        return flow
    }

}