package com.thedasagroup.suminative.data.model.request.kds_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CreateKDSOrderRequest(
    @SerialName("businessId")
    val businessId: Int,
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("tableName")
    val tableName: String,
    @SerialName("cartJson")
    val cartJson: String,
    @SerialName("orderStatus")
    val orderStatus: Int,
    @SerialName("createdBy")
    val createdBy: Int
)
