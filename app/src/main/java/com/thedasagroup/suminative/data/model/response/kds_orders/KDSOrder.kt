package com.thedasagroup.suminative.data.model.response.kds_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class KDSOrder(
    @SerialName("id")
    val id: Int? = null,
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("storeId")
    val storeId: Int? = null,
    @SerialName("customerId")
    val customerId: Int? = null,
    @SerialName("tableId")
    val tableId: Int? = null,
    @SerialName("tableName")
    val tableName: String? = null,
    @SerialName("cartJson")
    val cartJson: String? = null,
    @SerialName("orderStatus")
    val orderStatus: Int? = null,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("createdBy")
    val createdBy: Int? = null
)
